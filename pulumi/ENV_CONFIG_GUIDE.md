# A1D Agent 环境变量配置指南

本指南说明如何在 Pulumi 中配置 A1D Agent 所需的所有环境变量。

## 🤖 自动化工具 (推荐)

我们提供了自动化工具来简化环境变量配置过程：

### 快速开始

```bash
# 1. 测试工具是否正常工作
./scripts/test-env-config.sh

# 2. 自动设置 Pulumi 环境变量
./scripts/auto-set-pulumi-env.sh
```

### 工具说明

- **`pulumi/utils/env-config.ts`**: 核心工具，从 `src/lib/env.ts` 自动读取 schema
- **`scripts/auto-set-pulumi-env.sh`**: 自动设置脚本，使用 dotenvx 读取本地环境变量
- **`scripts/test-env-config.sh`**: 测试工具是否正常工作

### 工具命令

```bash
# 查看环境变量 schema
bun pulumi/utils/env-config.ts schema

# 生成 pulumi config set 命令
bun pulumi/utils/env-config.ts commands

# 生成 Pulumi secrets 配置代码
bun pulumi/utils/env-config.ts secrets

# 生成 Pulumi 环境变量配置代码
bun pulumi/utils/env-config.ts env
```

## 必需的环境变量

这些环境变量是应用运行的必需项：

```bash
# 数据库配置
pulumi config set --secret postgresurl "postgresql://user:password@host:port/database"

# Convex 配置
pulumi config set --secret convexurl "https://your-deployment.convex.cloud"
```

## 可选的 AI 服务 API 密钥

根据你使用的 AI 服务，设置相应的 API 密钥：

### Minimax
```bash
pulumi config set --secret minimaxapikey "your-minimax-api-key"
pulumi config set --secret minimaxgroupid "your-minimax-group-id"
```

### 其他 AI 服务
```bash
# SpeedPainter
pulumi config set --secret speedpainterapikey "your-speedpainter-key"

# X 302
pulumi config set --secret x302apikey "your-x302-key"

# 慧眼 API (多个服务)
pulumi config set --secret huiyanaapikey "your-huiyan-a-key"  # Claude + OpenAI
pulumi config set --secret huiyanbapikey "your-huiyan-b-key"  # Claude
pulumi config set --secret huiyancapikey "your-huiyan-c-key"  # Midjourney

# WaveSpeed
pulumi config set --secret wavespeedapikey "your-wavespeed-key"

# 其他 AI 服务
pulumi config set --secret falapikey "your-fal-key"
pulumi config set --secret groqapikey "your-groq-key"
pulumi config set --secret openrouterapikey "your-openrouter-key"
pulumi config set --secret xaiapikey "your-xai-key"
pulumi config set --secret geminiapikey "your-gemini-key"
pulumi config set --secret anthropicapikey "your-anthropic-key"
```

## Cloudflare R2 配置

如果使用 Cloudflare R2 存储：

```bash
pulumi config set --secret cloudflareaccountid "your-cloudflare-account-id"
pulumi config set --secret cloudflareaccess keyid "your-cloudflare-access-key-id"
pulumi config set --secret cloudflaresecretkey "your-cloudflare-secret-key"

# R2 存储桶名称 (可选，默认为 'dev')
pulumi config set --secret cloudflarebucketname "your-bucket-name"
```

## 批量配置脚本

你可以创建一个脚本来批量设置环境变量：

```bash
#!/bin/bash
# set-env-vars.sh

# 必需变量
pulumi config set --secret postgresurl "$POSTGRES_URL"
pulumi config set --secret convexurl "$CONVEX_URL"

# AI 服务 (如果环境变量存在则设置)
[ ! -z "$MINIMAX_API_KEY" ] && pulumi config set --secret minimaxapikey "$MINIMAX_API_KEY"
[ ! -z "$MINIMAX_GROUP_ID" ] && pulumi config set --secret minimaxgroupid "$MINIMAX_GROUP_ID"
[ ! -z "$ANTHROPIC_API_KEY" ] && pulumi config set --secret anthropicapikey "$ANTHROPIC_API_KEY"
[ ! -z "$GROQ_API_KEY" ] && pulumi config set --secret groqapikey "$GROQ_API_KEY"
# ... 添加其他变量

# Cloudflare R2
[ ! -z "$CLOUDFLARE_ACCOUNT_ID" ] && pulumi config set --secret cloudflareaccountid "$CLOUDFLARE_ACCOUNT_ID"
[ ! -z "$CLOUDFLARE_ACCESS_KEY_ID" ] && pulumi config set --secret cloudflareaccess keyid "$CLOUDFLARE_ACCESS_KEY_ID"
[ ! -z "$CLOUDFLARE_SECRET_KEY" ] && pulumi config set --secret cloudflaresecretkey "$CLOUDFLARE_SECRET_KEY"
[ ! -z "$CLOUDFLARE_R2_BUCKET_NAME" ] && pulumi config set --secret cloudflarebucketname "$CLOUDFLARE_R2_BUCKET_NAME"

echo "环境变量配置完成！"
```

## 查看已配置的变量

查看当前配置的环境变量：

```bash
# 查看所有配置 (不显示密钥值)
pulumi config

# 查看特定配置的值 (显示密钥)
pulumi config get --show-secrets postgresurl
```

## 环境变量映射

Pulumi 配置键到环境变量的映射：

| Pulumi 配置键 | 环境变量名 | 是否必需 |
|--------------|-----------|---------|
| `postgresurl` | `POSTGRES_URL` | ✅ |
| `convexurl` | `CONVEX_URL` | ✅ |
| `minimaxapikey` | `MINIMAX_API_KEY` | ❌ |
| `minimaxgroupid` | `MINIMAX_GROUP_ID` | ❌ |
| `anthropicapikey` | `ANTHROPIC_API_KEY` | ❌ |
| `groqapikey` | `GROQ_API_KEY` | ❌ |
| `cloudflareaccountid` | `CLOUDFLARE_ACCOUNT_ID` | ❌ |
| ... | ... | ... |

## 注意事项

1. **安全性**: 所有敏感信息都使用 `--secret` 标志加密存储
2. **可选性**: 只有 `POSTGRES_URL` 和 `CONVEX_URL` 是必需的，其他都是可选的
3. **默认值**: `CLOUDFLARE_R2_BUCKET_NAME` 如果未设置，默认为 `dev`
4. **命名规则**: Pulumi 配置键使用小写字母，环境变量使用大写字母和下划线

## 部署

配置完环境变量后，运行部署：

```bash
cd pulumi
pulumi up
```

或使用部署脚本：

```bash
./scripts/deploy-a1d-agent.sh
```
