#!/usr/bin/env bun
/// <reference types="@types/bun" />
import { get } from '@dotenvx/dotenvx'
import { JsonObject } from 'arktype'

import { runtimeEnvSchema } from '../../src/lib/env'

export type EnvVarInfo = {
  key: string
  value: any
  required: boolean
  defaultValue?: string
}

export function getEnvSchema(): {
  required: EnvVarInfo[]
  optional: EnvVarInfo[]
} {
  const schema = runtimeEnvSchema.json as any

  const required: EnvVarInfo[] = []
  const optional: EnvVarInfo[] = []

  if (schema.required) {
    for (const item of schema.required) {
      required.push({
        key: item.key,
        value: item.value,
        required: true,
      })
    }
  }

  if (schema.optional) {
    for (const item of schema.optional) {
      optional.push({
        key: item.key,
        value: item.value,
        required: false,
        defaultValue: item.default,
      })
    }
  }

  return { required, optional }
}

export function envKeyToConfigKey(envKey: string): string {
  return envKey.toLowerCase().replace(/_/g, '')
}

export function envKeyToSecretName(envKey: string): string {
  return envKey.toLowerCase().replace(/_/g, '-')
}

export function getEnvValue(envKey: string): string | undefined {
  try {
    return get(envKey) || process.env[envKey]
  } catch {
    return process.env[envKey]
  }
}

export function generatePulumiConfigCommands(): string[] {
  const { required, optional } = getEnvSchema()
  const commands: string[] = []

  // 生成必需变量的配置命令
  for (const envVar of required) {
    const configKey = envKeyToConfigKey(envVar.key)
    const value = getEnvValue(envVar.key)

    if (value) {
      commands.push(`pulumi config set --secret ${configKey} "${value}"`)
    } else {
      commands.push(
        `# Missing required: pulumi config set --secret ${configKey} "YOUR_${envVar.key}"`,
      )
    }
  }

  // 生成可选变量的配置命令
  for (const envVar of optional) {
    const configKey = envKeyToConfigKey(envVar.key)
    const value = getEnvValue(envVar.key)

    if (value) {
      commands.push(`pulumi config set --secret ${configKey} "${value}"`)
    } else if (envVar.defaultValue) {
      commands.push(
        `# Optional with default: pulumi config set --secret ${configKey} "${envVar.defaultValue}"`,
      )
    } else {
      commands.push(
        `# Optional: pulumi config set --secret ${configKey} "YOUR_${envVar.key}"`,
      )
    }
  }

  return commands
}

export function generatePulumiSecrets(): Array<{ name: string; value: any }> {
  const { required, optional } = getEnvSchema()
  const secrets: Array<{ name: string; value: any }> = []

  for (const envVar of [...required, ...optional]) {
    const secretName = envKeyToSecretName(envVar.key)
    const configKey = envKeyToConfigKey(envVar.key)

    if (envVar.required) {
      secrets.push({
        name: secretName,
        value: `config.requireSecret('${configKey}')`,
      })
    } else {
      const defaultValue = envVar.defaultValue
        ? `'${envVar.defaultValue}'`
        : 'undefined'
      secrets.push({
        name: secretName,
        value: `config.getSecret('${configKey}') || ${defaultValue}`,
      })
    }
  }

  return secrets
}

export function generatePulumiEnvVars(): Array<{
  name: string
  secretRef: string
}> {
  const { required, optional } = getEnvSchema()
  const envVars: Array<{ name: string; secretRef: string }> = []

  for (const envVar of [...required, ...optional]) {
    const secretName = envKeyToSecretName(envVar.key)
    envVars.push({
      name: envVar.key,
      secretRef: secretName,
    })
  }

  return envVars
}

// CLI 功能
if (import.meta.main) {
  const command = process.argv[2]

  switch (command) {
    case 'schema':
      console.log(JSON.stringify(getEnvSchema(), null, 2))
      break

    case 'commands':
      const commands = generatePulumiConfigCommands()
      console.log('#!/bin/bash')
      console.log('# Auto-generated Pulumi config commands')
      console.log('')
      commands.forEach((cmd) => console.log(cmd))
      break

    case 'secrets':
      const secrets = generatePulumiSecrets()
      console.log('// Auto-generated Pulumi secrets configuration')
      secrets.forEach((secret) => {
        console.log(`  {`)
        console.log(`    name: '${secret.name}',`)
        console.log(`    value: ${secret.value},`)
        console.log(`  },`)
      })
      break

    case 'env':
      const envVars = generatePulumiEnvVars()
      console.log('// Auto-generated Pulumi environment variables')
      envVars.forEach((env) => {
        console.log(`  {`)
        console.log(`    name: '${env.name}',`)
        console.log(`    secretRef: '${env.secretRef}',`)
        console.log(`  },`)
      })
      break

    default:
      console.log('Usage: bun pulumi/utils/env-config.ts <command>')
      console.log('Commands:')
      console.log('  schema    - Show environment schema')
      console.log('  commands  - Generate pulumi config set commands')
      console.log('  secrets   - Generate Pulumi secrets configuration')
      console.log(
        '  env       - Generate Pulumi environment variables configuration',
      )
  }
}
