name: a1d-agent
runtime: nodejs
description: A1D Agent AI service deployment using Azure Container Apps

config:
  # Infrastructure stack reference
  a1d-agent:infraStackRef:
    description: 'Reference to the shared infrastructure stack (format: org/project/stack)'
    default: 'ethan-huo-org/a1d-pulumi-azure/azure-dev'

  # Application configuration
  a1d-agent:appName:
    description: 'Name of the A1D Agent application'
    default: 'a1d-agent'

  a1d-agent:containerImage:
    description: 'Container image to deploy'
    default: 'a1dazureacr.azurecr.io/a1d-agent:latest'

  a1d-agent:environment:
    description: 'Deployment environment'
    default: 'production'

  # Resource allocation for LLM chat app
  a1d-agent:cpu:
    description: 'CPU allocation (in cores) - sufficient for chat app (mainly API forwarding)'
    default: 0.5

  a1d-agent:memory:
    description: 'Memory allocation (in Gi) - sufficient for Node.js chat app'
    default: 1

  a1d-agent:port:
    description: 'Application port - match Dockerfile EXPOSE'
    default: 4111

  # Scaling configuration - Minimal scaling for chat app
  a1d-agent:minReplicas:
    description: 'Minimum number of replicas - keep 1 instance running for availability'
    default: 1

  a1d-agent:maxReplicas:
    description: 'Maximum number of replicas - mainly for zero-downtime deployment'
    default: 2

  a1d-agent:scaleToZero:
    description: 'Allow scaling to zero - disabled for always-on service'
    default: false

  # Domain configuration
  a1d-agent:usesEnvironmentLevelDomains:
    description: 'Use environment-level domains (automatic domain assignment)'
    default: true

  # Required environment variables (secrets)
  # Set these using: pulumi config set --secret <key> <value>
  # Or use the automation script: ./scripts/auto-set-pulumi-env.sh

  # Database
  a1d-agent:postgres_url:
    description: 'PostgreSQL database URL'
    secret: true

  a1d-agent:convex_url:
    description: 'Convex deployment URL'
    secret: true

  # AI Service API Keys (all optional - set only the ones you use)
  a1d-agent:anthropic_api_key:
    description: 'Anthropic API key for Claude'
    secret: true

  a1d-agent:groq_api_key:
    description: 'Groq API key'
    secret: true

  a1d-agent:gemini_api_key:
    description: 'Google Gemini API key'
    secret: true

  a1d-agent:fal_api_key:
    description: 'Fal API key'
    secret: true

  a1d-agent:xai_api_key:
    description: 'xAI API key'
    secret: true

  a1d-agent:openrouter_api_key:
    description: 'OpenRouter API key'
    secret: true

  a1d-agent:minimax_api_key:
    description: 'Minimax API key'
    secret: true

  a1d-agent:minimax_group_id:
    description: 'Minimax Group ID'
    secret: true

  a1d-agent:huiyan_a_api_key:
    description: 'Huiyan A API key (Claude + OpenAI)'
    secret: true

  a1d-agent:huiyan_b_api_key:
    description: 'Huiyan B API key (Claude)'
    secret: true

  a1d-agent:huiyan_c_api_key:
    description: 'Huiyan C API key (Midjourney)'
    secret: true

  a1d-agent:speedpainter_api_key:
    description: 'SpeedPainter API key'
    secret: true

  a1d-agent:wavespeed_api_key:
    description: 'WaveSpeed API key'
    secret: true

  a1d-agent:x_302_api_key:
    description: 'X 302 API key'
    secret: true

  # Cloudflare R2 Configuration
  a1d-agent:cloudflare_account_id:
    description: 'Cloudflare Account ID'
    secret: true

  a1d-agent:cloudflare_access_key_id:
    description: 'Cloudflare R2 Access Key ID'
    secret: true

  a1d-agent:cloudflare_secret_key:
    description: 'Cloudflare R2 Secret Key'
    secret: true

  a1d-agent:cloudflare_r2_bucket_name:
    description: 'Cloudflare R2 Bucket Name'
    default: 'dev'
    secret: true

template:
  config:
    pulumi:tags:
      value:
        Project: 'a1d-agent'
        Environment: 'production'
        Component: 'ai-agent-service'
        Platform: 'azure'
        Service: 'mastra-ai-agent'
        AlwaysOn: 'true'
