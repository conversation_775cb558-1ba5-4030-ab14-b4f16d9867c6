# 🤖 Pulumi 环境变量自动化配置

## 概述

我们创建了一套自动化工具来简化 Pulumi 环境变量配置过程。这些工具直接从 `src/lib/env.ts` 的 schema 读取配置，确保 Pulumi 配置与应用代码保持同步。

## 🚀 快速开始

```bash
# 1. 测试工具
./scripts/test-env-config.sh

# 2. 自动设置环境变量
./scripts/auto-set-pulumi-env.sh
```

## 📁 文件结构

```
pulumi/
├── utils/
│   └── env-config.ts          # 核心工具：从 env.ts schema 生成配置
├── index.ts                   # 更新后的 Pulumi 配置，使用自动生成的配置
├── ENV_CONFIG_GUIDE.md        # 详细配置指南
└── README_ENV_AUTOMATION.md   # 本文档

scripts/
├── auto-set-pulumi-env.sh     # 自动设置脚本
└── test-env-config.sh         # 测试脚本
```

## 🔧 工具功能

### `pulumi/utils/env-config.ts`

核心工具，提供以下功能：

```bash
# 查看环境变量 schema
bun pulumi/utils/env-config.ts schema

# 生成 pulumi config set 命令
bun pulumi/utils/env-config.ts commands

# 生成 Pulumi secrets 配置代码
bun pulumi/utils/env-config.ts secrets

# 生成 Pulumi 环境变量配置代码
bun pulumi/utils/env-config.ts env
```

### 自动化特性

1. **Schema 同步**: 直接从 `src/lib/env.ts` 读取 schema，确保配置同步
2. **环境变量读取**: 使用 `@dotenvx/dotenvx` 读取加密的环境变量
3. **智能配置**: 自动区分必需和可选变量
4. **代码生成**: 自动生成 Pulumi 配置代码

## 📋 配置映射

工具自动处理以下映射：

| 环境变量 | Pulumi 配置键 | Secret 名称 |
|---------|-------------|------------|
| `POSTGRES_URL` | `postgresurl` | `postgres-url` |
| `CONVEX_URL` | `convexurl` | `convex-url` |
| `ANTHROPIC_API_KEY` | `anthropicapikey` | `anthropic-api-key` |
| `CLOUDFLARE_ACCOUNT_ID` | `cloudflareaccountid` | `cloudflare-account-id` |
| ... | ... | ... |

## 🔄 工作流程

1. **开发阶段**: 在 `src/lib/env.ts` 中定义环境变量
2. **配置阶段**: 运行 `./scripts/auto-set-pulumi-env.sh` 自动设置 Pulumi 配置
3. **部署阶段**: Pulumi 自动使用生成的配置部署应用

## ✅ 优势

- **自动同步**: 环境变量定义与 Pulumi 配置自动同步
- **减少错误**: 避免手动配置时的拼写错误
- **简化维护**: 只需在一个地方维护环境变量定义
- **类型安全**: 利用 TypeScript 和 arktype 的类型检查

## 🛠️ 自定义

如需自定义配置逻辑，可以修改 `pulumi/utils/env-config.ts` 中的函数：

- `getEnvSchema()`: 获取环境变量 schema
- `envKeyToConfigKey()`: 环境变量名到配置键的转换
- `envKeyToSecretName()`: 环境变量名到 secret 名称的转换
- `generatePulumiConfigCommands()`: 生成配置命令

## 🔍 故障排除

### 工具无法读取环境变量

确保：
1. 安装了 `@dotenvx/dotenvx`
2. `.env` 文件存在且格式正确
3. 运行 `dotenvx run -- bun scripts/env-check.ts` 验证环境变量

### Pulumi 配置失败

检查：
1. Pulumi CLI 已安装
2. 在正确的 stack 中
3. 环境变量值格式正确（特别是 URL 格式）

### 生成的配置不正确

验证：
1. `src/lib/env.ts` 中的 schema 定义正确
2. 运行 `bun pulumi/utils/env-config.ts schema` 检查解析结果
3. 检查环境变量名称拼写

## 📚 相关文档

- [详细配置指南](./ENV_CONFIG_GUIDE.md)
- [部署脚本说明](../scripts/deploy-a1d-agent.sh)
- [环境变量定义](../src/lib/env.ts)
