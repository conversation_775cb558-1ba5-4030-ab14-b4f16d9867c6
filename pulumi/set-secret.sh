#!/bin/bash
# Auto-generated Pulumi config commands

pulumi config set --secret anthropicApiKey "************************************************************************************************************"
pulumi config set --secret cloudflareAccessKeyId "c4edfb5965fb91f88fe73d4bf8053626"
pulumi config set --secret cloudflareAccountId "2da2eb61458c5f71b03602c372d3f703"
pulumi config set --secret cloudflareSecretKey "c55d2262357560d88a9cc46381f9578969c9b5b8a479bf9ff6319f65f540acb7"
pulumi config set --secret convexUrl "https://peaceful-poodle-78.convex.cloud"
pulumi config set --secret falApiKey "75bf20c8-37a6-4d8b-8dcd-11a339710fc2:d658eae94f4245eac63846eb0bcf09a9"
pulumi config set --secret geminiApiKey "AIzaSyBFjH6D06AIE18cSzTRMIKHcop-B90LRuw"
pulumi config set --secret groqApiKey "********************************************************"
pulumi config set --secret huiyanAApiKey "sk-iKsdyEAXK2nZiV1OVAExG7Eehu3GA0TRCCMXJXCWaiiFmGJG"
pulumi config set --secret huiyanBApiKey "sk-iKsdyEAXK2nZiV1OVAExG7Eehu3GA0TRCCMXJXCWaiiFmGJG

# https://console.x.ai/
# 来源: <EMAIL>
# 额度: /月
XAI_API_KEY=************************************************************************************

# https://aistudio.google.com/app/prompts/new_chat
# 来源: <EMAIL>
GEMINI_API_KEY=AIzaSyBFjH6D06AIE18cSzTRMIKHcop-B90LRuw

# Vertex.ai
# TODO

# Fal
# 来源: <EMAIL>
FAL_API_KEY=75bf20c8-37a6-4d8b-8dcd-11a339710fc2:d658eae94f4245eac63846eb0bcf09a9


ANTHROPIC_API_KEY=************************************************************************************************************


# Speedpainter
SPEEDPAINTER_API_KEY=jCevatD-5PK2PLmOS4Blc

# https://platform.deepseek.com
DEEPSEEK_API_KEY="***********************************"
pulumi config set --secret huiyanCApiKey "sk-O4giLkyCbrZpSKBzzbHUtoTrkafSyHQXJtdcYmCr6dmtO4xs"
pulumi config set --secret minimaxApiKey "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
pulumi config set --secret minimaxGroupId "1878043912631751010"
pulumi config set --secret openrouterApiKey "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"
pulumi config set --secret postgresUrl "postgresql://postgres@localhost:5432/postgres"
pulumi config set --secret speedpainterApiKey "jCevatD-5PK2PLmOS4Blc"
pulumi config set --secret wavespeedApiKey "ee85d21a5516051b9cd0456881eb0c47f0b653d323e2bca6eefc59ac2399f2d2"
pulumi config set --secret xaiApiKey "************************************************************************************"
pulumi config set --secret x302ApiKey "sk-O6g0EgQ4M9kUMfIs3kJAFpt5NUPR2kYwsRF2tkeYTO5csOcJ"
# Optional with default: pulumi config set --secret cloudflareR2BucketName "dev"
