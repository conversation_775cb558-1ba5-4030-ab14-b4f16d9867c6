#!/bin/bash

# A1D Agent Deployment Script
# This script helps deploy the A1D Agent to Azure Container Apps using Pulumi

set -e

echo "🚀 A1D Agent Deployment Script"
echo "================================"

# Check if we're in the right directory
if [ ! -f "pulumi/index.ts" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    exit 1
fi

# Check if Pulumi is installed
if ! command -v pulumi &> /dev/null; then
    echo "❌ Error: Pulumi is not installed. Please install Pulumi first."
    echo "Visit: https://www.pulumi.com/docs/get-started/install/"
    exit 1
fi

# Navigate to pulumi directory
cd pulumi

# Check if stack exists, create if not
STACK_NAME="dev"  # Use 'dev' as default stack name
if ! pulumi stack ls | grep -q "$STACK_NAME"; then
    echo "📦 Creating new Pulumi stack: $STACK_NAME"
    pulumi stack init $STACK_NAME
else
    echo "📦 Using existing Pulumi stack: $STACK_NAME"
    pulumi stack select $STACK_NAME
fi

# Check if required secrets are set
echo "🔐 Checking required secrets..."

check_secret() {
    local secret_name=$1
    if ! pulumi config get $secret_name --show-secrets &> /dev/null; then
        echo "❌ Missing required secret: $secret_name"
        echo "Please set it using: pulumi config set --secret $secret_name <value>"
        return 1
    else
        echo "✅ Secret $secret_name is configured"
        return 0
    fi
}

MISSING_SECRETS=false

if ! check_secret "postgresurl"; then
    MISSING_SECRETS=true
fi

if ! check_secret "convexurl"; then
    MISSING_SECRETS=true
fi

if [ "$MISSING_SECRETS" = true ]; then
    echo ""
    echo "🔧 Example commands to set required secrets:"
    echo "pulumi config set --secret postgresurl 'postgresql://user:pass@host:port/db'"
    echo "pulumi config set --secret convexurl 'https://your-convex-deployment.convex.cloud'"
    echo ""
    echo "💡 You can also set additional API keys for integrations:"
    echo "pulumi config set --secret minimaxapikey 'your-minimax-key'"
    echo "pulumi config set --secret anthropicapikey 'your-anthropic-key'"
    echo "pulumi config set --secret groqapikey 'your-groq-key'"
    echo "pulumi config set --secret cloudflareaccountid 'your-cloudflare-account-id'"
    echo ""
    echo "📖 For complete configuration guide, see: pulumi/ENV_CONFIG_GUIDE.md"
    echo ""
    exit 1
fi

# Optional: Check if container image exists
CONTAINER_IMAGE=$(pulumi config get containerImage || echo "a1dazureacr.azurecr.io/a1d-agent:latest")
echo "📦 Using container image: $CONTAINER_IMAGE"

# Deploy
echo "🚀 Starting deployment..."
echo "This may take several minutes..."

if pulumi up --yes; then
    echo ""
    echo "✅ Deployment successful!"
    echo ""
    echo "📋 Application Information:"
    pulumi stack output applicationInfo
    echo ""
    echo "🌐 Endpoints:"
    echo "Health Check: $(pulumi stack output environmentHealthCheck)"
    echo "Mastra API: $(pulumi stack output environmentMastraEndpoint)"
    echo "Main URL: $(pulumi stack output environmentDomainUrl)"
    echo ""
    echo "🎉 Your A1D Agent is now running!"
else
    echo "❌ Deployment failed. Please check the error messages above."
    exit 1
fi
