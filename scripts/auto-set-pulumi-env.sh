#!/bin/bash

# 自动从环境变量设置 Pulumi 配置的脚本
# 使用 env-config.ts 工具自动生成配置命令

set -e

echo "🤖 A1D Agent 自动 Pulumi 环境变量设置"
echo "====================================="

# 检查是否在正确的目录
if [ ! -f "pulumi/index.ts" ]; then
    echo "❌ 错误: 请从项目根目录运行此脚本"
    exit 1
fi

# 检查是否安装了 Pulumi
if ! command -v pulumi &> /dev/null; then
    echo "❌ 错误: 未安装 Pulumi。请先安装 Pulumi。"
    echo "访问: https://www.pulumi.com/docs/get-started/install/"
    exit 1
fi

# 检查是否安装了 bun
if ! command -v bun &> /dev/null; then
    echo "❌ 错误: 未安装 bun。请先安装 bun。"
    echo "访问: https://bun.sh/"
    exit 1
fi

# 进入 pulumi 目录
cd pulumi

echo ""
echo "📋 分析环境变量 schema..."

# 生成配置命令
TEMP_SCRIPT=$(mktemp)
bun utils/env-config.ts commands > "$TEMP_SCRIPT"

echo ""
echo "🔧 生成的配置命令:"
echo "=================="
cat "$TEMP_SCRIPT"

echo ""
read -p "是否执行这些配置命令? (y/N): " -n 1 -r
echo

if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo ""
    echo "⚡ 执行配置命令..."
    
    # 执行生成的脚本，但跳过注释行和 shebang
    while IFS= read -r line; do
        # 跳过注释行和空行
        if [[ ! "$line" =~ ^[[:space:]]*# ]] && [[ -n "$line" ]] && [[ ! "$line" =~ ^#!/ ]]; then
            echo "执行: $line"
            eval "$line"
        elif [[ "$line" =~ ^# ]]; then
            echo "跳过: $line"
        fi
    done < "$TEMP_SCRIPT"
    
    echo ""
    echo "✅ 配置完成！"
    echo ""
    echo "📋 查看已配置的变量:"
    pulumi config
    
else
    echo ""
    echo "⏭️  跳过执行。你可以手动运行生成的命令。"
fi

# 清理临时文件
rm -f "$TEMP_SCRIPT"

echo ""
echo "🚀 下一步:"
echo "  1. 检查配置: pulumi config"
echo "  2. 部署应用: pulumi up"
echo "  3. 或使用部署脚本: ../scripts/deploy-a1d-agent.sh"
