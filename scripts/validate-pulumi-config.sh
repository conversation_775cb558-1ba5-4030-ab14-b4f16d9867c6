#!/bin/bash

# 验证 Pulumi 配置的脚本

set -e

echo "🔍 验证 Pulumi 配置"
echo "=================="

# 检查是否在正确的目录
if [ ! -f "pulumi/index.ts" ]; then
    echo "❌ 错误: 请从项目根目录运行此脚本"
    exit 1
fi

# 检查是否安装了 Pulumi
if ! command -v pulumi &> /dev/null; then
    echo "❌ 错误: 未安装 Pulumi。请先安装 Pulumi。"
    exit 1
fi

# 进入 pulumi 目录
cd pulumi

echo ""
echo "📋 当前 Pulumi 项目信息:"
echo "======================"
pulumi about

echo ""
echo "📦 当前 stack 信息:"
echo "=================="
pulumi stack --show-name || echo "❌ 没有选择 stack"

echo ""
echo "🔧 当前配置:"
echo "==========="
pulumi config || echo "❌ 无法读取配置"

echo ""
echo "🔐 检查必需的密钥:"
echo "================"

check_secret() {
    local secret_name=$1
    if pulumi config get $secret_name --show-secrets &> /dev/null; then
        echo "✅ $secret_name: 已配置"
        return 0
    else
        echo "❌ $secret_name: 未配置"
        return 1
    fi
}

MISSING_SECRETS=false

# 检查必需的密钥
if ! check_secret "postgresurl"; then
    MISSING_SECRETS=true
fi

if ! check_secret "convexurl"; then
    MISSING_SECRETS=true
fi

echo ""
echo "🤖 检查可选的 AI 服务密钥:"
echo "======================="

# 检查可选的 AI 服务密钥
optional_keys=(
    "anthropicapikey"
    "groqapikey" 
    "geminiapikey"
    "falapikey"
    "xaiapikey"
    "openrouterapikey"
    "minimaxapikey"
    "minimaxgroupid"
    "huiyanaapikey"
    "huiyanbapikey"
    "huiyancapikey"
    "speedpainterapikey"
    "wavespeedapikey"
    "x302apikey"
    "cloudflareaccountid"
    "cloudflareaccesskeyid"
    "cloudflaresecretkey"
    "cloudflarer2bucketname"
)

configured_count=0
for key in "${optional_keys[@]}"; do
    if pulumi config get $key --show-secrets &> /dev/null; then
        echo "✅ $key: 已配置"
        ((configured_count++))
    else
        echo "⏭️  $key: 未配置 (可选)"
    fi
done

echo ""
echo "📊 配置总结:"
echo "==========="
echo "必需密钥: $([ "$MISSING_SECRETS" = false ] && echo "✅ 全部配置" || echo "❌ 缺少必需密钥")"
echo "可选密钥: $configured_count/${#optional_keys[@]} 已配置"

if [ "$MISSING_SECRETS" = true ]; then
    echo ""
    echo "⚠️  缺少必需的环境变量，请运行:"
    echo "   ./scripts/auto-set-pulumi-env.sh"
    echo "   或手动设置缺少的密钥"
    exit 1
fi

echo ""
echo "✅ Pulumi 配置验证完成！"
echo ""
echo "🚀 下一步:"
echo "   pulumi up  # 部署应用"
