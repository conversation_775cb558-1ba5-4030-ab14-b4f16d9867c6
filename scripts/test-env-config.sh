#!/bin/bash

# 测试环境变量配置工具

echo "🧪 测试环境变量配置工具"
echo "======================"

# 检查是否安装了 bun
if ! command -v bun &> /dev/null; then
    echo "❌ 错误: 未安装 bun。请先安装 bun。"
    exit 1
fi

echo ""
echo "📋 1. 测试 schema 输出:"
echo "====================="
bun pulumi/utils/env-config.ts schema

echo ""
echo "🔧 2. 测试生成配置命令:"
echo "======================"
bun pulumi/utils/env-config.ts commands

echo ""
echo "🔐 3. 测试生成 secrets 配置:"
echo "=========================="
bun pulumi/utils/env-config.ts secrets

echo ""
echo "🌍 4. 测试生成环境变量配置:"
echo "========================="
bun pulumi/utils/env-config.ts env

echo ""
echo "✅ 测试完成！"
